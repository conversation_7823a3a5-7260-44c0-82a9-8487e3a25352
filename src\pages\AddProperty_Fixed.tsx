import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useProperties } from '@/hooks/useProperties';
import { useAuth } from '@/hooks/useAuth';
import { PropertyFormData, ROOM_TYPES } from '@/types/property';
import { Upload, X, Plus, Building, Home, Copy, Trash2 } from 'lucide-react';
import LocationInput from '@/components/LocationInput';
import { ZoneInput } from '@/components/ZoneInput';
import Navigation from '@/components/Navigation';

const KENYAN_COUNTIES = [
  'Nairobi', 'Mombasa', 'Kisumu', 'Nakuru', 'Eldoret', 'Thika', 'Machakos', 'Meru',
  'Nyeri', 'Garissa', 'Kakamega', 'Kitale', 'Malindi', 'Lamu', 'Wajir', 'Marsabit',
  'Isiolo', 'Moyale', 'Lodwar', 'Kapenguria', 'Bungoma', 'Webuye', 'Busia',
  'Migori', 'Kisii', 'Kericho', 'Bomet', 'Narok', 'Kajiado', 'Kiambu', 'Murang\'a',
  'Kirinyaga', 'Nanyuki', 'Embu', 'Mwingi', 'Kitui', 'Makueni',
  'Kilifi', 'Kwale', 'Taita Taveta', 'Tana River', 'Mandera', 'Turkana', 'Samburu',
  'Laikipia', 'Baringo', 'Elgeyo Marakwet', 'Nandi', 'Trans Nzoia', 'Uasin Gishu',
  'West Pokot', 'Vihiga', 'Siaya', 'Homa Bay', 'Nyamira'
];

const COMMON_AMENITIES = [
  'Swimming Pool', 'Gym', 'Parking', 'Security', 'Garden', 'Balcony',
  'Air Conditioning', 'Heating', 'Internet/WiFi', 'Cable TV', 'Laundry',
  'Elevator', 'Backup Generator', 'Water Tank', 'CCTV', 'Gated Community'
];

const UNIT_AMENITIES = [
  'Furnished', 'Semi-Furnished', 'Kitchen Appliances', 'Balcony', 'Terrace',
  'Walk-in Closet', 'En-suite Bathroom', 'Bathtub', 'Air Conditioning'
];

interface UnitFormData {
  unit_name: string;
  room_type: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  rent: number;
  deposit: number;
  is_available: boolean;
  unit_amenities: string[];
  unit_description: string;
  floor_number: number;
  unit_number: string;
}

interface EnhancedPropertyFormData extends PropertyFormData {
  is_multi_unit: boolean;
  building_name?: string;
  property_type: 'single_unit' | 'multi_unit';
  units: UnitFormData[];
}

const AddProperty: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { createProperty, uploadPropertyImage } = useProperties();
  
  const [activeTab, setActiveTab] = useState('basic');
  const [formData, setFormData] = useState<EnhancedPropertyFormData>({
    title: '',
    description: '',
    rent: 0,
    location: '',
    county: '',
    zone: '',
    room_type: '',
    bedrooms: 1,
    bathrooms: 1,
    area: 0,
    amenities: [],
    available: true,
    featured: false,
    latitude: undefined,
    longitude: undefined,
    formatted_address: undefined,
    neighborhood: undefined,
    city: undefined,
    is_multi_unit: false,
    building_name: '',
    property_type: 'single_unit',
    units: []
  });
  
  const [images, setImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize with one unit for single unit properties
  React.useEffect(() => {
    if (formData.property_type === 'single_unit' && formData.units.length === 0) {
      setFormData(prev => ({
        ...prev,
        units: [{
          unit_name: '',
          room_type: '',
          bedrooms: 1,
          bathrooms: 1,
          area: 0,
          rent: 0,
          deposit: 0,
          is_available: true,
          unit_amenities: [],
          unit_description: '',
          floor_number: 1,
          unit_number: ''
        }]
      }));
    }
  }, [formData.property_type, formData.units.length]);

  // Redirect if not authenticated
  React.useEffect(() => {
    if (!user) {
      navigate('/auth');
    }
  }, [user, navigate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  const handleLocationChange = (locationData: {
    formatted_address?: string;
    address?: string;
    lat?: number;
    lng?: number;
    neighborhood?: string;
    city?: string;
  }) => {
    setFormData(prev => ({
      ...prev,
      location: locationData.formatted_address || locationData.address || '',
      latitude: locationData.lat,
      longitude: locationData.lng,
      formatted_address: locationData.formatted_address,
      neighborhood: locationData.neighborhood,
      city: locationData.city
    }));
  };

  const handleAmenityToggle = (amenity: string) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.includes(amenity)
        ? prev.amenities.filter(a => a !== amenity)
        : [...prev.amenities, amenity]
    }));
  };

  const handlePropertyTypeChange = (type: 'single_unit' | 'multi_unit') => {
    setFormData(prev => ({
      ...prev,
      property_type: type,
      is_multi_unit: type === 'multi_unit',
      units: type === 'single_unit' ? [{
        unit_name: '',
        room_type: '',
        bedrooms: 1,
        bathrooms: 1,
        area: 0,
        rent: 0,
        deposit: 0,
        is_available: true,
        unit_amenities: [],
        unit_description: '',
        floor_number: 1,
        unit_number: ''
      }] : []
    }));
  };

  const addUnit = () => {
    setFormData(prev => ({
      ...prev,
      units: [...prev.units, {
        unit_name: '',
        room_type: '',
        bedrooms: 1,
        bathrooms: 1,
        area: 0,
        rent: 0,
        deposit: 0,
        is_available: true,
        unit_amenities: [],
        unit_description: '',
        floor_number: 1,
        unit_number: ''
      }]
    }));
  };

  const removeUnit = (index: number) => {
    setFormData(prev => ({
      ...prev,
      units: prev.units.filter((_, i) => i !== index)
    }));
  };

  const duplicateUnit = (index: number) => {
    const unitToDuplicate = formData.units[index];
    setFormData(prev => ({
      ...prev,
      units: [...prev.units, { ...unitToDuplicate, unit_name: `${unitToDuplicate.unit_name} (Copy)` }]
    }));
  };

  const updateUnit = (index: number, field: keyof UnitFormData, value: string | number | boolean | string[]) => {
    setFormData(prev => ({
      ...prev,
      units: prev.units.map((unit, i) => 
        i === index ? { ...unit, [field]: value } : unit
      )
    }));
  };

  const handleUnitAmenityToggle = (unitIndex: number, amenity: string) => {
    const unit = formData.units[unitIndex];
    const newAmenities = unit.unit_amenities.includes(amenity)
      ? unit.unit_amenities.filter(a => a !== amenity)
      : [...unit.unit_amenities, amenity];
    
    updateUnit(unitIndex, 'unit_amenities', newAmenities);
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length + images.length > 10) {
      alert('Maximum 10 images allowed');
      return;
    }

    setImages(prev => [...prev, ...files]);
    
    // Create previews
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) newErrors.title = 'Title is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.location.trim()) newErrors.location = 'Location is required';
    if (!formData.county) newErrors.county = 'County is required';
    
    if (formData.property_type === 'single_unit') {
      if (!formData.room_type) newErrors.room_type = 'Room type is required';
      if (formData.rent <= 0) newErrors.rent = 'Valid rent amount is required';
    }

    if (formData.property_type === 'multi_unit') {
      if (!formData.building_name?.trim()) newErrors.building_name = 'Building name is required';
      if (formData.units.length === 0) newErrors.units = 'At least one unit is required';
      
      formData.units.forEach((unit, index) => {
        if (!unit.room_type) newErrors[`unit_${index}_room_type`] = `Unit ${index + 1} room type is required`;
        if (unit.rent <= 0) newErrors[`unit_${index}_rent`] = `Unit ${index + 1} rent is required`;
      });
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    
    try {
      // Create the property
      const property = await createProperty({
        ...formData,
        latitude: formData.latitude,
        longitude: formData.longitude,
        formatted_address: formData.formatted_address,
        neighborhood: formData.neighborhood,
        city: formData.city
      } as PropertyFormData);

      // Upload images
      for (const image of images) {
        await uploadPropertyImage(property.id, image);
      }

      navigate('/my-properties');
    } catch (error) {
      console.error('Error creating property:', error);
      alert('Failed to create property. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navigation />
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-6 w-6" />
              Add New Property
            </CardTitle>
            <CardDescription>
              Create a new property listing for rent
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="units">Units</TabsTrigger>
                  <TabsTrigger value="amenities">Amenities</TabsTrigger>
                  <TabsTrigger value="images">Images</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-6">
                  {/* Property Type Selection */}
                  <div className="space-y-4">
                    <Label className="text-base font-semibold">Property Type</Label>
                    <div className="grid grid-cols-2 gap-4">
                      <Card 
                        className={`cursor-pointer transition-all ${
                          formData.property_type === 'single_unit' 
                            ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950' 
                            : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                        }`}
                        onClick={() => handlePropertyTypeChange('single_unit')}
                      >
                        <CardContent className="p-4 text-center">
                          <Home className="h-8 w-8 mx-auto mb-2" />
                          <h3 className="font-semibold">Single Unit</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            One unit property (house, apartment, etc.)
                          </p>
                        </CardContent>
                      </Card>
                      
                      <Card 
                        className={`cursor-pointer transition-all ${
                          formData.property_type === 'multi_unit' 
                            ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950' 
                            : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                        }`}
                        onClick={() => handlePropertyTypeChange('multi_unit')}
                      >
                        <CardContent className="p-4 text-center">
                          <Building className="h-8 w-8 mx-auto mb-2" />
                          <h3 className="font-semibold">Multi Unit</h3>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Building with multiple rental units
                          </p>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  {/* Basic Property Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="title">Property Title *</Label>
                      <Input
                        id="title"
                        name="title"
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder="Enter property title"
                        className={errors.title ? 'border-red-500' : ''}
                      />
                      {errors.title && <p className="text-sm text-red-500">{errors.title}</p>}
                    </div>

                    {formData.property_type === 'multi_unit' && (
                      <div className="space-y-2">
                        <Label htmlFor="building_name">Building Name *</Label>
                        <Input
                          id="building_name"
                          name="building_name"
                          value={formData.building_name}
                          onChange={handleInputChange}
                          placeholder="Enter building name"
                          className={errors.building_name ? 'border-red-500' : ''}
                        />
                        {errors.building_name && <p className="text-sm text-red-500">{errors.building_name}</p>}
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor="county">County *</Label>
                      <Select onValueChange={(value) => setFormData(prev => ({ ...prev, county: value }))}>
                        <SelectTrigger className={errors.county ? 'border-red-500' : ''}>
                          <SelectValue placeholder="Select county" />
                        </SelectTrigger>
                        <SelectContent>
                          {KENYAN_COUNTIES.map(county => (
                            <SelectItem key={county} value={county}>{county}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.county && <p className="text-sm text-red-500">{errors.county}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="zone">Zone/Area</Label>
                      <ZoneInput
                        value={formData.zone || ''}
                        onChange={(value) => setFormData(prev => ({ ...prev, zone: value }))}
                        county={formData.county}
                        placeholder="Enter zone or area (e.g., Westlands, Karen, Kilimani)"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="location">Location *</Label>
                    <LocationInput
                      value={formData.location}
                      onChange={(value, coordinates) => {
                        handleLocationChange({
                          formatted_address: value,
                          address: value,
                          lat: coordinates?.lat,
                          lng: coordinates?.lng
                        });
                      }}
                      placeholder="Enter property address"
                      className={errors.location ? 'border-red-500' : ''}
                    />
                    {errors.location && <p className="text-sm text-red-500">{errors.location}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description *</Label>
                    <Textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Describe the property..."
                      rows={4}
                      className={errors.description ? 'border-red-500' : ''}
                    />
                    {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
                  </div>

                  {/* Single Unit Specific Fields */}
                  {formData.property_type === 'single_unit' && (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="room_type">Room Type *</Label>
                        <Select onValueChange={(value) => setFormData(prev => ({ ...prev, room_type: value }))}>
                          <SelectTrigger className={errors.room_type ? 'border-red-500' : ''}>
                            <SelectValue placeholder="Select room type" />
                          </SelectTrigger>
                          <SelectContent>
                            {ROOM_TYPES.map(type => (
                              <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {errors.room_type && <p className="text-sm text-red-500">{errors.room_type}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="rent">Monthly Rent (KSH) *</Label>
                        <Input
                          id="rent"
                          name="rent"
                          type="number"
                          value={formData.rent}
                          onChange={handleInputChange}
                          placeholder="Enter rent amount"
                          className={errors.rent ? 'border-red-500' : ''}
                        />
                        {errors.rent && <p className="text-sm text-red-500">{errors.rent}</p>}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="area">Area (sq ft)</Label>
                        <Input
                          id="area"
                          name="area"
                          type="number"
                          value={formData.area}
                          onChange={handleInputChange}
                          placeholder="Property area"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="bedrooms">Bedrooms</Label>
                        <Input
                          id="bedrooms"
                          name="bedrooms"
                          type="number"
                          min="0"
                          value={formData.bedrooms}
                          onChange={handleInputChange}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="bathrooms">Bathrooms</Label>
                        <Input
                          id="bathrooms"
                          name="bathrooms"
                          type="number"
                          min="1"
                          value={formData.bathrooms}
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="units" className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">
                      {formData.property_type === 'single_unit' ? 'Unit Details' : 'Property Units'}
                    </h3>
                    {formData.property_type === 'multi_unit' && (
                      <Button type="button" onClick={addUnit} variant="outline" size="sm">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Unit
                      </Button>
                    )}
                  </div>

                  {errors.units && <p className="text-sm text-red-500">{errors.units}</p>}

                  {formData.units.map((unit, index) => (
                    <Card key={index} className="relative">
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-base">
                            {formData.property_type === 'single_unit' ? 'Unit Details' : `Unit ${index + 1}`}
                          </CardTitle>
                          {formData.property_type === 'multi_unit' && formData.units.length > 1 && (
                            <div className="flex gap-2">
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => duplicateUnit(index)}
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => removeUnit(index)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          )}
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {formData.property_type === 'multi_unit' && (
                            <>
                              <div className="space-y-2">
                                <Label>Unit Name</Label>
                                <Input
                                  value={unit.unit_name}
                                  onChange={(e) => updateUnit(index, 'unit_name', e.target.value)}
                                  placeholder="e.g., Unit A1, Studio 1"
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Unit Number</Label>
                                <Input
                                  value={unit.unit_number}
                                  onChange={(e) => updateUnit(index, 'unit_number', e.target.value)}
                                  placeholder="e.g., A1, 101"
                                />
                              </div>
                              <div className="space-y-2">
                                <Label>Floor Number</Label>
                                <Input
                                  type="number"
                                  min="0"
                                  value={unit.floor_number}
                                  onChange={(e) => updateUnit(index, 'floor_number', parseInt(e.target.value) || 0)}
                                />
                              </div>
                            </>
                          )}

                          <div className="space-y-2">
                            <Label>Room Type *</Label>
                            <Select onValueChange={(value) => updateUnit(index, 'room_type', value)}>
                              <SelectTrigger className={errors[`unit_${index}_room_type`] ? 'border-red-500' : ''}>
                                <SelectValue placeholder="Select room type" />
                              </SelectTrigger>
                              <SelectContent>
                                {ROOM_TYPES.map(type => (
                                  <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            {errors[`unit_${index}_room_type`] && (
                              <p className="text-sm text-red-500">{errors[`unit_${index}_room_type`]}</p>
                            )}
                          </div>

                          <div className="space-y-2">
                            <Label>Monthly Rent (KSH) *</Label>
                            <Input
                              type="number"
                              value={unit.rent}
                              onChange={(e) => updateUnit(index, 'rent', parseFloat(e.target.value) || 0)}
                              placeholder="Enter rent amount"
                              className={errors[`unit_${index}_rent`] ? 'border-red-500' : ''}
                            />
                            {errors[`unit_${index}_rent`] && (
                              <p className="text-sm text-red-500">{errors[`unit_${index}_rent`]}</p>
                            )}
                          </div>

                          <div className="space-y-2">
                            <Label>Security Deposit (KSH)</Label>
                            <Input
                              type="number"
                              value={unit.deposit}
                              onChange={(e) => updateUnit(index, 'deposit', parseFloat(e.target.value) || 0)}
                              placeholder="Enter deposit amount"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Bedrooms</Label>
                            <Input
                              type="number"
                              min="0"
                              value={unit.bedrooms}
                              onChange={(e) => updateUnit(index, 'bedrooms', parseInt(e.target.value) || 0)}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Bathrooms</Label>
                            <Input
                              type="number"
                              min="1"
                              value={unit.bathrooms}
                              onChange={(e) => updateUnit(index, 'bathrooms', parseInt(e.target.value) || 1)}
                            />
                          </div>

                          <div className="space-y-2">
                            <Label>Area (sq ft)</Label>
                            <Input
                              type="number"
                              value={unit.area}
                              onChange={(e) => updateUnit(index, 'area', parseFloat(e.target.value) || 0)}
                              placeholder="Unit area"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label>Unit Description</Label>
                          <Textarea
                            value={unit.unit_description}
                            onChange={(e) => updateUnit(index, 'unit_description', e.target.value)}
                            placeholder="Describe this unit..."
                            rows={3}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Unit Amenities</Label>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                            {UNIT_AMENITIES.map(amenity => (
                              <div key={amenity} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`unit-${index}-${amenity}`}
                                  checked={unit.unit_amenities.includes(amenity)}
                                  onCheckedChange={() => handleUnitAmenityToggle(index, amenity)}
                                />
                                <Label htmlFor={`unit-${index}-${amenity}`} className="text-sm">
                                  {amenity}
                                </Label>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`available-${index}`}
                            checked={unit.is_available}
                            onCheckedChange={(checked) => updateUnit(index, 'is_available', checked)}
                          />
                          <Label htmlFor={`available-${index}`}>Unit is currently available</Label>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </TabsContent>

                <TabsContent value="amenities" className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Property Amenities</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {COMMON_AMENITIES.map(amenity => (
                        <div key={amenity} className="flex items-center space-x-2">
                          <Checkbox
                            id={amenity}
                            checked={formData.amenities.includes(amenity)}
                            onCheckedChange={() => handleAmenityToggle(amenity)}
                          />
                          <Label htmlFor={amenity} className="text-sm">
                            {amenity}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="featured"
                      checked={formData.featured}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, featured: !!checked }))}
                    />
                    <Label htmlFor="featured">Feature this property</Label>
                  </div>
                </TabsContent>

                <TabsContent value="images" className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Property Images</h3>
                    <div className="space-y-4">
                      <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6">
                        <Upload className="h-8 w-8 mx-auto mb-4 text-gray-400" />
                        <div className="space-y-4">
                          <p className="text-center text-sm text-gray-500">
                            Add photos to showcase your property
                          </p>
                          
                          {/* Upload Options */}
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                            <Button
                              type="button"
                              variant="outline"
                              className="w-full"
                              onClick={() => document.getElementById('camera-input')?.click()}
                            >
                              <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                                <circle cx="12" cy="13" r="4"/>
                              </svg>
                              Take Photo
                            </Button>
                            
                            <Button
                              type="button"
                              variant="outline"
                              className="w-full"
                              onClick={() => document.getElementById('gallery-input')?.click()}
                            >
                              <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                                <circle cx="9" cy="9" r="2"/>
                                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
                              </svg>
                              Choose from Gallery
                            </Button>
                            
                            <Button
                              type="button"
                              variant="outline"
                              className="w-full"
                              onClick={() => document.getElementById('files-input')?.click()}
                            >
                              <Upload className="h-4 w-4 mr-2" />
                              Browse Files
                            </Button>
                          </div>
                          
                          {/* Hidden file inputs */}
                          <Input
                            id="camera-input"
                            type="file"
                            multiple
                            accept="image/*"
                            capture="environment"
                            onChange={handleImageUpload}
                            className="hidden"
                          />
                          <Input
                            id="gallery-input"
                            type="file"
                            multiple
                            accept="image/*"
                            onChange={handleImageUpload}
                            className="hidden"
                          />
                          <Input
                            id="files-input"
                            type="file"
                            multiple
                            accept="image/*"
                            onChange={handleImageUpload}
                            className="hidden"
                          />
                          
                          <p className="text-center text-xs text-gray-500">
                            Maximum 10 images, 5MB each • Supported formats: JPG, PNG, WebP
                          </p>
                        </div>
                      </div>

                      {imagePreviews.length > 0 && (
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                          {imagePreviews.map((preview, index) => (
                            <div key={index} className="relative group">
                              <img
                                src={preview}
                                alt={`Preview ${index + 1}`}
                                className="w-full h-32 object-cover rounded-lg"
                              />
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => removeImage(index)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              <div className="flex gap-4 pt-6 border-t">
                <Button type="button" variant="outline" onClick={() => navigate('/')}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading} className="ml-auto">
                  {loading ? 'Creating...' : 'Create Property'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AddProperty;
